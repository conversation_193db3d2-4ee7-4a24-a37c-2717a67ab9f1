/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* CSS变量定义 */
:root {
    --header-height: 2px; /* 修正为实际头部高度 */
    --footer-height: 0px; /* 没有footer */
    --content-padding: 20px; /* 减少padding */
    --available-height: calc(100vh - var(--header-height) - var(--footer-height) - var(--content-padding));
}

/* 全局隐藏滚动条但保留滚动功能 */
html {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

html::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

body {
    margin: 0;
    padding: 0;
    font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif;
    background: #0a0e1a;
    color: #ffffff;
    overflow: hidden; /* 完全禁止滚动 */
    position: relative;
    height: 100vh; /* 固定高度，不允许超出 */
    /* 隐藏滚动条但保留滚动功能 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

body::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

/* Three.js背景容器 */
#three-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

/* 主容器 */
.dashboard-container {
    position: relative;
    z-index: 1;
    height: 100vh; /* 固定高度 */
    background: linear-gradient(135deg, rgba(10, 14, 26, 0.95), rgba(20, 30, 48, 0.9));
    backdrop-filter: blur(10px);
    overflow: hidden; /* 完全禁止滚动 */
    /* 隐藏滚动条但保留滚动功能 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

.dashboard-container::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

/* 顶部标题栏 */
.header {
    background: transparent; /* 移除背景 */
    border-bottom: 2px solid #4fd1c7; /* 保留蓝色底边框 */
    padding: 0; /* 移除内边距 */
    height: 2px; /* 只保留边框的高度 */
    display: block; /* 改为block，不显示内容 */
    box-shadow: none; /* 移除阴影 */
}

/* 隐藏header内容 */
.header > * {
    display: none;
}

.header-left {
    flex: 1;
    display: none; /* 隐藏左侧内容 */
    justify-content: flex-start;
}

.header-right {
    flex: 1;
    display: flex; /* 显示右侧内容 */
    justify-content: flex-end;
    align-items: center;
    gap: 15px;
}

.main-title {
    display: none; /* 隐藏主标题 */
    flex: 2;
    text-align: center;
    margin: 0;
    font-size: 2.2rem;
    font-weight: bold;
    background: linear-gradient(45deg, #4fd1c7, #48bb78);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 2px 10px rgba(79, 209, 199, 0.3);
}



/* 时间信息样式 */
.current-time {
    display: flex;
    align-items: center;
    gap: 12px;
    color: #ffffff;
    font-size: 0.9rem;
    padding: 8px 16px;
    background: rgba(79, 209, 199, 0.1);
    border: 1px solid rgba(79, 209, 199, 0.3);
    border-radius: 20px;
}

.current-time .date {
    color: #4fd1c7;
    font-weight: 500;
}

.current-time .time {
    color: #48bb78;
    font-weight: bold;
    font-size: 1.1rem;
    font-family: 'Courier New', monospace;
}

/* 后台管理链接样式 */
.admin-link-container {
    display: flex;
    align-items: center;
}

.admin-link {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #ffffff;
    text-decoration: none;
    font-size: 0.9rem;
    padding: 8px 16px;
    background: rgba(79, 209, 199, 0.1);
    border: 1px solid rgba(79, 209, 199, 0.3);
    border-radius: 20px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.admin-link:hover {
    background: rgba(79, 209, 199, 0.2);
    border-color: #4fd1c7;
    color: #4fd1c7;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(79, 209, 199, 0.3);
}

.admin-link svg {
    transition: all 0.3s ease;
}

.admin-link:hover svg {
    transform: rotate(90deg);
    color: #4fd1c7;
}

.admin-link span {
    font-weight: 500;
}

/* 主要内容区域 */
.main-content {
    display: flex;
    flex-direction: column;
    gap: 15px;
    padding: 10px;
    padding-bottom: 10px; /* 减少底部内边距 */
    height: calc(100vh - 12px); /* 直接使用视口高度减去头部和padding */
    max-height: calc(100vh - 12px);
    max-width: 100vw;
    box-sizing: border-box;
    overflow: hidden; /* 禁止滚动 */
}

/* 双面板容器 */
.dual-panel-container {
    display: grid;
    grid-template-columns: 1fr 1fr minmax(280px, 350px);
    gap: 15px;
    flex: 1;
    overflow: hidden; /* 禁止滚动 */
    min-height: 0; /* 允许flex子项缩小 */
}

/* 面板卡片通用样式 */
.panel-card {
    background: linear-gradient(145deg, rgba(26, 35, 50, 0.8), rgba(45, 55, 72, 0.6));
    /* border: 1px solid rgba(79, 209, 199, 0.3); */ /* 移除外边框 */
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 2vh; /* 改为视口高度的2% */
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.panel-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #4fd1c7, transparent);
    transition: left 0.5s ease;
}

.panel-card:hover::before {
    left: 100%;
}

.panel-card:hover {
    border-color: #4fd1c7;
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.4),
        0 0 20px rgba(79, 209, 199, 0.3);
    transform: translateY(-2px);
}

.panel-title {
    font-size: clamp(1rem, 2.5vw, 1.4rem); /* 自适应字体大小 */
    color: #4fd1c7;
    margin-bottom: max(1vw, 10px); /* 自适应边距 */
    text-shadow: 0 0 8px rgba(79, 209, 199, 0.5);
    border-bottom: 1px solid rgba(79, 209, 199, 0.3);
    padding-bottom: max(0.5vw, 6px); /* 自适应内边距 */
}

/* 设备列表表格样式 */
.device-list-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden; /* 禁止滚动 */
    min-height: 0; /* 允许flex子项缩小 */
}

.device-table-container {
    height: 70vh; /* 固定高度为视口高度的70% */
    margin-bottom: 20px; /* 增加底部间距确保边框完全可见 */
    overflow-x: auto;
    overflow-y: auto;
    background: rgba(26, 32, 44, 0.8);
    border-radius: 12px;
    border: 1px solid #4fd1c7; /* 使用细蓝色边框 */
    box-shadow: 0 0 10px rgba(79, 209, 199, 0.5), inset 0 0 5px rgba(79, 209, 199, 0.2); /* 添加发光效果 */
    backdrop-filter: blur(10px);
    max-width: 100%;
    min-height: 70vh; /* 最小高度也设为70vh，确保固定大小 */
    max-height: 70vh; /* 最大高度也设为70vh，确保固定大小 */
    box-sizing: border-box;
    width: 100%;
    position: relative;
    padding: 5px; /* 增加内边距，确保3px边框完全可见 */
}

.device-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
    color: #e2e8f0;
    /* 确保表格不会超出容器 */
    table-layout: fixed;
    max-width: 100%;
    /* 移除min-width，让表格完全适应容器 */
    /* 强制表格适应容器 */
    box-sizing: border-box;
    position: relative;
}

.device-table thead {
    background: rgba(45, 55, 72, 0.9);
    position: sticky;
    top: 0;
    z-index: 10;
}

.device-table th {
    padding: 12px 8px;
    text-align: center;
    font-weight: 600;
    color: #4fd1c7;
    border-bottom: 2px solid rgba(79, 209, 199, 0.5);
    white-space: nowrap;
    /* 防止表头溢出 */
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 0; /* 配合table-layout: fixed使用 */
}

.device-table tbody tr {
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(79, 209, 199, 0.1);
    cursor: pointer;
}

.device-table tbody tr:hover {
    background: rgba(79, 209, 199, 0.1);
    transform: scale(1.01);
}

.device-table tbody tr:active {
    transform: scale(0.99);
}

.device-table td {
    padding: 10px 8px;
    text-align: center;
    border-right: 1px solid rgba(79, 209, 199, 0.1);
    /* 防止单元格内容溢出 */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 0; /* 配合table-layout: fixed使用 */
}

.device-table td:last-child {
    border-right: none;
}

/* 删除重复的列宽设置，使用下面统一的设置 */

/* 设备名称列 - 左对齐 */
.device-table .device-name {
    text-align: left;
    font-weight: 600;
    color: #ffffff;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 产品信息列 - 左对齐 */
.device-table .product-info {
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    /* 移除max-width，使用统一的列宽设置 */
}

/* 数值列样式 */
.device-table .production-count {
    color: #48bb78;
    font-weight: bold;
}

.device-table .current-oee,
.device-table .device-oee,
.device-table .utilization-rate {
    font-weight: bold;
}

/* 设备状态列样式 */
.device-table .device-status {
    font-weight: bold;
}

.device-table .device-status.status-running {
    color: #48bb78;
}

.device-table .device-status.status-idle {
    color: #ed8936;
}

.device-table .device-status.status-alarm {
    color: #f56565;
}

.device-table .device-status.status-offline {
    color: #a0aec0;
}

/* 故障信息列 */
.device-table .alarm-info {
    color: #f56565;
    font-weight: 500;
}

/* 加载状态 */
.device-table .loading-cell,
.device-table .no-data {
    text-align: center;
    padding: 20px;
    color: #a0aec0;
    font-style: italic;
}

/* 表格滚动条样式 */
.device-table-container::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.device-table-container::-webkit-scrollbar-track {
    background: rgba(45, 55, 72, 0.5);
    border-radius: 4px;
}

.device-table-container::-webkit-scrollbar-thumb {
    background: rgba(79, 209, 199, 0.6);
    border-radius: 4px;
}

.device-table-container::-webkit-scrollbar-thumb:hover {
    background: rgba(79, 209, 199, 0.8);
}

/* 删除重复的表格行样式，已在上面定义 */

/* 表格列宽设置 - 移除min-width防止表格超出容器 */
.device-table th:nth-child(1), /* 设备名称 */
.device-table td:nth-child(1) {
    width: 15%;
}

.device-table th:nth-child(2), /* 加工产品及工序 */
.device-table td:nth-child(2) {
    width: 25%;
}

.device-table th:nth-child(3), /* 今日产出 */
.device-table td:nth-child(3) {
    width: 10%;
}

.device-table th:nth-child(4), /* 当前OEE */
.device-table td:nth-child(4) {
    width: 10%;
}

.device-table th:nth-child(5), /* 设备OEE */
.device-table td:nth-child(5) {
    width: 10%;
}

.device-table th:nth-child(6), /* 开机率 */
.device-table td:nth-child(6) {
    width: 10%;
}

.device-table th:nth-child(7), /* 设备状态 */
.device-table td:nth-child(7) {
    width: 10%;
}



/* 移除了生产统计卡片样式 */

/* 移除了图表卡片和生产统计相关样式 */



.stats-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 保持2x2网格 */
    gap: 8px; /* 减少基础间距 */
    width: 100%;
    max-width: 320px; /* 基础最大宽度 */
    flex: 1; /* 填充剩余空间 */
}

.stat-item {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 10px 8px; /* 增加内边距 */
    background: rgba(79, 209, 199, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(79, 209, 199, 0.2);
    text-align: center;
    min-height: 65px; /* 增加最小高度 */
    transition: all 0.3s ease;
}

.stat-item:hover {
    background: rgba(79, 209, 199, 0.15);
    border-color: #4fd1c7;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(79, 209, 199, 0.3);
}

.stat-label {
    color: #a0aec0;
    font-size: 0.85rem; /* 增大字体 */
    margin-bottom: 6px; /* 增加间距 */
    line-height: 1.2;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5); /* 添加文字阴影增强可读性 */
}

.stat-value {
    color: #4fd1c7;
    font-size: 1.2rem; /* 增大数值字体 */
    font-weight: bold;
    text-shadow: 0 0 8px rgba(79, 209, 199, 0.7), 0 1px 2px rgba(0, 0, 0, 0.5); /* 增强文字阴影 */
    line-height: 1;
}

/* 移除了生产统计段落样式 */

/* 生产统计项样式 */
.production-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 0;
    border-bottom: 1px solid rgba(79, 209, 199, 0.1);
}

.production-item:last-child {
    border-bottom: none;
}

.production-item .label {
    color: #a0aec0;
    font-size: 0.85rem;
}

.production-item .value {
    color: #4fd1c7;
    font-weight: bold;
    font-size: 1rem;
    text-shadow: 0 0 6px rgba(79, 209, 199, 0.5);
}

/* 加载和错误状态 */
.loading, .error, .no-data {
    text-align: center;
    color: #a0aec0;
    font-style: italic;
    padding: 15px;
}

.loading {
    color: #4fd1c7;
}

.error {
    color: #f56565;
}

.loading-card {
    grid-column: 1 / -1;
    text-align: center;
    padding: 20px;
    color: #4fd1c7;
    background: rgba(79, 209, 199, 0.1);
    border-radius: 10px;
    border: 1px solid rgba(79, 209, 199, 0.3);
    font-style: italic;
}

.chart-container {
    display: flex;
    justify-content: center;
    margin-top: 10px;
    height: 180px;
    position: relative;
}

.chart-container canvas {
    max-height: 180px;
    width: 100% !important;
    height: 100% !important;
}

/* 移除了图表卡片样式 */

/* 左右设备面板 */
.left-device-panel,
.right-device-panel {
    display: flex;
    flex-direction: column;
    gap: 15px;
    overflow: hidden; /* 禁止滚动 */
    background: linear-gradient(145deg, rgba(26, 35, 50, 0.8), rgba(45, 55, 72, 0.6));
    /* border: 1px solid rgba(79, 209, 199, 0.3); */ /* 移除外边框 */
    border-radius: 12px;
    padding: 15px;
}

/* 面板标题 */
.panel-title {
    color: #4fd1c7;
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
    text-align: center;
}

/* 统计卡片 */
.stats-cards {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
}

.stat-card {
    background: linear-gradient(145deg, rgba(26, 35, 50, 0.9), rgba(45, 55, 72, 0.7));
    border-radius: 15px;
    padding: 18px;
    text-align: center;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    position: relative;
    overflow: visible;
    box-sizing: border-box;
}

/* 可点击的统计卡片样式 */
.stat-card.clickable {
    cursor: pointer;
}

.stat-card.clickable:hover {
    transform: translateY(-3px);
    /* 移除光影效果 */
    background: linear-gradient(145deg, rgba(26, 35, 50, 0.95), rgba(45, 55, 72, 0.85));
}

.stat-card.clickable:active {
    transform: translateY(-1px);
    /* 移除光影效果 */
}

/* 移除光影效果 */

.stat-card.running {
    border-color: #48bb78;
    /* 移除光影效果 */
}

.stat-card.idle {
    border-color: #ed8936;
    /* 移除光影效果 */
}

.stat-card.alarm {
    border-color: #f56565;
    /* 移除光影效果 */
}

.stat-card.offline {
    border-color: #a0aec0;
    /* 移除光影效果 */
}

.stat-card h4 {
    font-size: 1rem;
    margin-bottom: 10px;
    color: #e2e8f0;
    font-weight: normal;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    display: block;
}

.stat-card.running .stat-number { color: #48bb78; }
.stat-card.idle .stat-number { color: #ed8936; }
.stat-card.alarm .stat-number { color: #f56565; }
.stat-card.offline .stat-number { color: #a0aec0; }

/* 设备信息区域 */
.device-info {
    background: rgba(79, 209, 199, 0.1);
    border: 1px solid rgba(79, 209, 199, 0.3);
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
}

.device-info p {
    margin-bottom: 8px;
    color: #e2e8f0;
    line-height: 1.5;
}

/* 设备区域头部样式 */
.device-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    gap: 15px;
}

.header-spacer {
    flex: 1;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.device-list-section h3 {
    color: #4fd1c7;
    font-size: 1.5rem;
    margin: 0;
    text-shadow: 0 0 10px rgba(79, 209, 199, 0.5);
}

/* 设备卡片区域 */
.device-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 15px;
    margin-top: 20px;
    max-height: calc(100vh - 400px);
    overflow-y: auto;
    padding-right: 5px;
}

.device-card {
    background: linear-gradient(145deg, rgba(26, 35, 50, 0.8), rgba(45, 55, 72, 0.6));
    border: 1px solid rgba(79, 209, 199, 0.3);
    border-radius: 10px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.device-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border-color: #4fd1c7;
}

.device-card.status-running { border-left: 4px solid #48bb78; }
.device-card.status-idle { border-left: 4px solid #ed8936; }
.device-card.status-alarm { border-left: 4px solid #f56565; }
.device-card.status-offline { border-left: 4px solid #a0aec0; }
.device-card.status-unknown { border-left: 4px solid #4fd1c7; }

/* 新设备卡片样式 */
.device-card-new {
    background: rgba(45, 55, 72, 0.9);
    border: 1px solid rgba(79, 209, 199, 0.3);
    border-radius: 12px;
    padding: 16px;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    min-height: 160px;
    display: flex;
    flex-direction: column;
}

.device-card-new:hover {
    border-color: #4fd1c7;
    box-shadow: 0 8px 25px rgba(79, 209, 199, 0.4);
    transform: translateY(-3px);
}

/* 设备头部 */
.device-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.device-id {
    background: rgba(79, 209, 199, 0.2);
    color: #4fd1c7;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.85em;
    font-weight: bold;
}

.status-badge {
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 0.75em;
    font-weight: bold;
    text-transform: uppercase;
}

.status-badge.running {
    background: linear-gradient(145deg, #48bb78, #38a169);
    color: white;
    animation: glow 2s ease-in-out infinite alternate;
}

.status-badge.idle {
    background: linear-gradient(145deg, #ed8936, #dd6b20);
    color: white;
}

.status-badge.alarm {
    background: linear-gradient(145deg, #f56565, #e53e3e);
    color: white;
    animation: blink-badge 1s infinite;
}

.status-badge.offline {
    background: linear-gradient(145deg, #a0aec0, #718096);
    color: white;
    opacity: 0.8;
}

.status-badge.unknown {
    background: linear-gradient(145deg, #4fd1c7, #38b2ac);
    color: white;
    opacity: 1;
    font-style: normal;
    border: none;
}

@keyframes blink-badge {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.6; }
}

@keyframes glow {
    0%, 100% { box-shadow: 0 0 5px rgba(79, 209, 199, 0.5); }
    50% { box-shadow: 0 0 20px rgba(79, 209, 199, 0.8); }
}

/* 设备名称和类型 */
.device-card-new .device-name {
    color: #ffffff;
    font-size: 1.1em;
    font-weight: bold;
    margin-bottom: 4px;
}



/* 执行程序显示 */
.execution-rate {
    color: #4fd1c7;
    font-size: 0.9rem;
    margin: 5px 0;
    font-weight: 500;
    text-shadow: 0 0 4px rgba(79, 209, 199, 0.3);
    transition: all 0.3s ease;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

/* 未知执行程序的特殊样式 - 改为标准样式 */
.execution-rate.unknown {
    color: #4fd1c7 !important;
    font-style: normal !important;
    opacity: 1 !important;
    text-shadow: 0 0 4px rgba(79, 209, 199, 0.3) !important;
    background: none !important;
    padding: 0 !important;
    border-radius: 0 !important;
    border: none !important;
}

/* 指标区域 */
.device-metrics {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.metric-row {
    display: flex;
    justify-content: space-between;
    gap: 8px;
}

.metric-item {
    flex: 1;
    text-align: center;
    padding: 8px;
    background: rgba(79, 209, 199, 0.1);
    border-radius: 6px;
    border: 1px solid rgba(79, 209, 199, 0.2);
}

.metric-value {
    color: #4fd1c7;
    font-size: 1.2em;
    font-weight: bold;
    margin-bottom: 2px;
}

.metric-label {
    color: #a0aec0;
    font-size: 0.75em;
}

/* 新设备卡片状态样式 */
.device-card-new.status-running {
    border-left: 5px solid #48bb78;
    box-shadow: 0 2px 12px rgba(72, 187, 120, 0.3);
}

.device-card-new.status-idle {
    border-left: 5px solid #ed8936;
    box-shadow: 0 2px 12px rgba(237, 137, 54, 0.3);
}

.device-card-new.status-alarm {
    border-left: 5px solid #f56565;
    box-shadow: 0 2px 12px rgba(245, 101, 101, 0.3);
    animation: pulse-alarm-new 2s infinite;
}

.device-card-new.status-offline {
    border-left: 5px solid #a0aec0;
    box-shadow: 0 2px 12px rgba(160, 174, 192, 0.2);
}

.device-card-new.status-unknown {
    border-left: 5px solid #4fd1c7;
    box-shadow: 0 2px 12px rgba(79, 209, 199, 0.3);
    opacity: 1;
}

@keyframes pulse-alarm-new {
    0%, 100% { box-shadow: 0 0 8px rgba(245, 101, 101, 0.5); }
    50% { box-shadow: 0 0 25px rgba(245, 101, 101, 0.8); }
}

/* 图表区域 */
.chart-section {
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(26, 35, 50, 0.5);
    border-radius: 15px;
    padding: 20px;
    border: 1px solid rgba(79, 209, 199, 0.2);
}

/* 右侧面板 */
.right-panel {
    display: flex;
    flex-direction: column;
    gap: 15px;
    max-height: calc(var(--available-height) - 80px);
    height: calc(var(--available-height) - 80px);
    overflow: hidden; /* 不显示滚动条 */
    padding-bottom: 0; /* 移除底部间隙，与左侧面板保持一致 */
}

/* 警告信息卡片 */
.alert-info-card {
    flex: 1;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    margin-bottom: 1.5vh; /* 改为视口高度的1.5% */
    border-radius: 16px; /* 增加圆角 */
    background: #222b3a; /* 深色背景 */
    padding: 16px 12px; /* 内边距 */
    box-shadow: 0 2px 8px rgba(0,0,0,0.12); /* 阴影提升层次感 */
}

.alert-info-card .latest-info-list {
    flex: 1;
    overflow-y: auto;
    padding-right: 0;
}

/* 美化报警信息列表项 */
.alert-info-card .alarm-info-item {
    padding: 8px 0;
    border-bottom: 1px solid #333;
    color: #fff;
    font-size: 14px;
}
.alert-info-card .alarm-info-item:last-child {
    border-bottom: none;
}

/* 完成率圆环 */
.completion-rate {
    text-align: center;
    padding: 20px;
}

.rate-circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: conic-gradient(#4fd1c7 0deg 280deg, rgba(79, 209, 199, 0.2) 280deg 360deg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    position: relative;
}

.rate-circle::before {
    content: '';
    position: absolute;
    width: 80px;
    height: 80px;
    background: #1a2332;
    border-radius: 50%;
    z-index: 1;
}

.rate-value {
    position: relative;
    z-index: 2;
    font-size: 1.8rem;
    font-weight: bold;
    color: #4fd1c7;
    text-shadow: 0 0 10px rgba(79, 209, 199, 0.5);
}

/* 新的安全生产日历样式 */
.safety-calendar-container {
    padding: 10px;
}

/* 安全生产日历卡片自适应高度 */
.calendar-panel-card {
    flex-shrink: 0; /* 不缩小 */
    max-height: 45vh; /* 改为视口高度的45% */
    min-height: 35vh; /* 最小高度保证35vh，确保日期完全显示 */
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.calendar-nav-btn {
    background: linear-gradient(145deg, #4fd1c7, #38b2ac);
    border: none;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    color: #ffffff;
    font-size: 1.2rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.calendar-nav-btn:hover {
    background: linear-gradient(145deg, #38b2ac, #2c7a7b);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(79, 209, 199, 0.4);
}

.calendar-title {
    color: #4fd1c7;
    font-size: 1.1rem;
    font-weight: bold;
    text-shadow: 0 0 8px rgba(79, 209, 199, 0.5);
}

.calendar-grid {
    background: rgba(45, 55, 72, 0.3);
    border-radius: 8px;
    padding: 10px;
    border: 1px solid rgba(79, 209, 199, 0.2);
}

.calendar-weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 2px;
    margin-bottom: 8px;
}

.weekday {
    text-align: center;
    padding: 8px 4px;
    font-size: 0.85rem;
    font-weight: bold;
    color: #4fd1c7;
    background: rgba(79, 209, 199, 0.1);
    border-radius: 4px;
}

.calendar-days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 2px;
}

.calendar-day {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.85rem;
    font-weight: 500;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: rgba(26, 35, 50, 0.5);
    border: 1px solid transparent;
    position: relative;
}

.calendar-day:hover {
    background: rgba(79, 209, 199, 0.2);
    border-color: #4fd1c7;
    transform: scale(1.05);
}

.calendar-day.today {
    background: linear-gradient(145deg, #4fd1c7, #38b2ac);
    color: #000000;
    font-weight: bold;
    box-shadow: 0 0 10px rgba(79, 209, 199, 0.6);
}

.calendar-day.has-alarm {
    background: linear-gradient(145deg, #f56565, #e53e3e);
    color: #ffffff;
    animation: pulse-alarm 2s infinite;
}

.calendar-day.has-alarm::after {
    content: '⚠';
    position: absolute;
    top: 2px;
    right: 2px;
    font-size: 0.6rem;
    color: #ffffff;
}

.calendar-day.other-month {
    color: #a0aec0;
    background: rgba(160, 174, 192, 0.1);
}

@keyframes pulse-alarm {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); box-shadow: 0 0 15px rgba(245, 101, 101, 0.8); }
}

.calendar-legend {
    display: flex;
    justify-content: space-around;
    margin-top: 10px;
    padding: 8px;
    background: rgba(79, 209, 199, 0.05);
    border-radius: 6px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.75rem;
    color: #a0aec0;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 3px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.legend-color.normal {
    background: rgba(26, 35, 50, 0.8);
}

.legend-color.alarm {
    background: linear-gradient(145deg, #f56565, #e53e3e);
}

.legend-color.today {
    background: linear-gradient(145deg, #4fd1c7, #38b2ac);
}

/* 弹窗样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.15); /* 与设备弹窗保持一致的透明度 */
    backdrop-filter: blur(1px); /* 与设备弹窗保持一致的模糊效果 */
}

/* 弹窗显示时禁止body滚动 */
body.modal-open {
    overflow: hidden;
    height: 100vh;
}

/* 设备状态模态窗口特殊样式 */
.device-status-modal-content {
    background: linear-gradient(145deg, rgba(26, 35, 50, 0.95), rgba(45, 55, 72, 0.9));
    margin: 2% auto;
    padding: 0;
    border: 1px solid rgba(79, 209, 199, 0.3);
    border-radius: 15px;
    width: 95%;
    max-width: 1400px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.device-status-modal-content .modal-header {
    background: linear-gradient(90deg, rgba(79, 209, 199, 0.2), rgba(72, 187, 120, 0.2));
    padding: 20px 30px;
    border-bottom: 1px solid rgba(79, 209, 199, 0.3);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.device-status-modal-content .modal-header h2,
.device-status-modal-content .modal-header h3 {
    margin: 0;
    color: #4fd1c7;
    font-size: 1.5rem;
    font-weight: bold;
}

.device-status-modal-content .modal-body {
    padding: 0;
    max-height: calc(90vh - 80px);
    overflow-y: auto;
}

.device-status-table-container {
    overflow-x: auto;
    overflow-y: auto;
    max-height: calc(90vh - 80px);
    max-width: 100%;
}

.device-status-table {
    width: 100%;
    border-collapse: collapse;
    background: transparent;
    /* 移除min-width，让表格完全适应容器 */
    table-layout: fixed;
    max-width: 100%;
}

.device-status-table th {
    background: linear-gradient(90deg, rgba(79, 209, 199, 0.3), rgba(72, 187, 120, 0.3));
    color: #e2e8f0;
    padding: 15px 12px;
    text-align: center;
    font-weight: bold;
    border-bottom: 2px solid rgba(79, 209, 199, 0.5);
    position: sticky;
    top: 0;
    z-index: 10;
}

.device-status-table td {
    padding: 12px;
    text-align: center;
    border-bottom: 1px solid rgba(79, 209, 199, 0.2);
    color: #e2e8f0;
}

.device-status-table tr:hover {
    background: rgba(79, 209, 199, 0.1);
}

.device-status-table .status-running { color: #48bb78; font-weight: bold; }
.device-status-table .status-idle { color: #ed8936; font-weight: bold; }
.device-status-table .status-alarm { color: #f56565; font-weight: bold; }
.device-status-table .status-offline { color: #a0aec0; font-weight: bold; }

.modal-content {
    background: linear-gradient(145deg, rgba(26, 35, 50, 0.95), rgba(45, 55, 72, 0.9));
    margin: 5% auto;
    padding: 0;
    border: 2px solid #4fd1c7;
    border-radius: 15px;
    width: 90%;
    max-width: 1400px;
    max-height: 85vh;
    overflow-y: auto;
    box-shadow:
        0 10px 50px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(79, 209, 199, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    animation: modalFadeIn 0.3s ease-out;
    position: relative;
}

/* 弹窗专用滚动条样式 */
.modal-content::-webkit-scrollbar {
    width: 12px;
}

.modal-content::-webkit-scrollbar-track {
    background: rgba(26, 35, 50, 0.8);
    border-radius: 6px;
}

.modal-content::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #4fd1c7, #3182ce);
    border-radius: 6px;
    border: 2px solid rgba(26, 35, 50, 0.8);
}

.modal-content::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #38b2ac, #2b77cb);
}

.modal-header {
    background: linear-gradient(135deg, #4fd1c7, #38b2ac);
    color: #000000;
    padding: 20px;
    border-radius: 13px 13px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.4rem;
    font-weight: bold;
}

.close {
    color: #000000;
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.close:hover {
    background: rgba(0, 0, 0, 0.2);
    transform: rotate(90deg);
}

.modal-body {
    padding: 25px;
    /* 确保内容区域有足够的底部边距 */
    padding-bottom: 40px;
}

.alarm-summary {
    margin-bottom: 25px;
}

.summary-card {
    background: rgba(79, 209, 199, 0.1);
    border: 1px solid rgba(79, 209, 199, 0.3);
    border-radius: 10px;
    padding: 20px;
}

.summary-card h4 {
    color: #4fd1c7;
    margin-bottom: 15px;
    font-size: 1.2rem;
    text-shadow: 0 0 8px rgba(79, 209, 199, 0.5);
}

.alarm-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
}

.stat-item-modal {
    text-align: center;
    padding: 15px;
    background: rgba(45, 55, 72, 0.6);
    border-radius: 8px;
    border: 1px solid rgba(79, 209, 199, 0.2);
}

.stat-item-modal .label {
    display: block;
    color: #a0aec0;
    font-size: 0.9rem;
    margin-bottom: 8px;
}

.stat-item-modal .value {
    display: block;
    color: #4fd1c7;
    font-size: 1.8rem;
    font-weight: bold;
    text-shadow: 0 0 10px rgba(79, 209, 199, 0.5);
}

.alarm-chart-section {
    margin-bottom: 25px;
}

.alarm-chart-section h4 {
    color: #4fd1c7;
    margin-bottom: 15px;
    font-size: 1.2rem;
    text-shadow: 0 0 8px rgba(79, 209, 199, 0.5);
}

.chart-container-modal {
    background: rgba(45, 55, 72, 0.4);
    border: 1px solid rgba(79, 209, 199, 0.2);
    border-radius: 10px;
    padding: 15px;
    text-align: center;
}

.alarm-details h4 {
    color: #4fd1c7;
    margin-bottom: 15px;
    font-size: 1.2rem;
    text-shadow: 0 0 8px rgba(79, 209, 199, 0.5);
}

.alarm-list {
    max-height: 300px;
    overflow-y: auto;
    /* 防止内部滚动区域的滚动事件冒泡 */
    overscroll-behavior: contain;
}

/* 报警列表专用滚动条 */
.alarm-list::-webkit-scrollbar {
    width: 8px;
}

.alarm-list::-webkit-scrollbar-track {
    background: rgba(45, 55, 72, 0.5);
    border-radius: 4px;
}

.alarm-list::-webkit-scrollbar-thumb {
    background: rgba(79, 209, 199, 0.6);
    border-radius: 4px;
}

.alarm-list::-webkit-scrollbar-thumb:hover {
    background: rgba(79, 209, 199, 0.8);
}

.alarm-item {
    background: rgba(45, 55, 72, 0.6);
    border: 1px solid rgba(79, 209, 199, 0.2);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 1vh; /* 改为视口高度的1% */
    transition: all 0.3s ease;
}

.alarm-item:hover {
    background: rgba(79, 209, 199, 0.1);
    border-color: #4fd1c7;
    transform: translateX(5px);
}

.alarm-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.alarm-device {
    color: #4fd1c7;
    font-weight: bold;
    font-size: 1rem;
}

.alarm-time {
    color: #a0aec0;
    font-size: 0.85rem;
}

.alarm-content {
    color: #ffffff;
    line-height: 1.4;
    font-size: 0.95rem;
}

.alarm-duration {
    color: #a0aec0;
    font-size: 0.8rem;
    margin-top: 5px;
    font-style: italic;
}

.alarm-severity {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
    margin-top: 8px;
}

.alarm-severity.high {
    background: linear-gradient(145deg, #f56565, #e53e3e);
    color: #ffffff;
}

.alarm-severity.medium {
    background: linear-gradient(145deg, #ed8936, #dd6b20);
    color: #ffffff;
}

.alarm-severity.low {
    background: linear-gradient(145deg, #ecc94b, #d69e2e);
    color: #000000;
}



/* 动画效果 */
@keyframes glow {
    from { box-shadow: 0 0 5px rgba(79, 209, 199, 0.5); }
    to { box-shadow: 0 0 20px rgba(79, 209, 199, 0.8); }
}

/* 移除光影动画 */

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(26, 35, 50, 0.5);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #4fd1c7, #3182ce);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #38b2ac, #2b77cb);
}

/* 响应式设计 - 1920*1080分辨率精确优化 */
@media screen and (min-width: 1600px) and (max-width: 1920px) {

    .stats-container {
        max-width: 300px; /* 稍微增大容器 */
        gap: 10px; /* 增加间距 */
    }

    .stat-item {
        min-height: 60px; /* 增加高度 */
        padding: 10px 8px; /* 增加内边距 */
    }

    .stat-label {
        font-size: 0.85rem; /* 增大字体 */
        margin-bottom: 6px; /* 增加间距 */
    }

    .stat-value {
        font-size: 1.15rem; /* 增大数值字体 */
    }
}

/* 响应式设计 - 1920*1080分辨率优化 */
@media screen and (max-width: 1920px) {
    .dual-panel-container {
        grid-template-columns: 1fr 1fr 280px;
        gap: 12px;
    }

    .main-content {
        gap: 12px;
        padding: 12px;
        padding-bottom: 20px; /* 保持底部20px距离 */
        max-height: calc(100vh - 80px); /* 确保不超出屏幕高度 */
    }

    .panel-card {
        padding: 10px;
        margin-bottom: 10px;
    }

    .device-card-new {
        min-height: 140px; /* 减小设备卡片高度 */
        padding: 12px;
    }

    .device-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); /* 缩小设备卡片最小宽度 */
        gap: 12px;
        max-height: calc(100vh - 350px); /* 调整设备网格高度 */
    }

    .stats-cards {
        grid-template-columns: repeat(4, 1fr); /* 保持4列但缩小间距 */
        gap: 10px;
        margin-bottom: 15px;
    }

    .stat-card {
        padding: 12px;
    }

    .stat-number {
        font-size: 1.8rem; /* 缩小数字字体 */
    }

    .stat-card h4 {
        font-size: 0.9rem; /* 缩小标题字体 */
    }

    .main-title {
        font-size: 1.6rem; /* 缩小主标题 */
    }

    .header {
        padding: 10px 15px; /* 缩小头部内边距 */
    }

    .current-time {
        padding: 5px 10px;
        font-size: 0.75rem; /* 缩小字体 */
    }



    .panel-title {
        font-size: 1rem; /* 缩小面板标题 */
        margin-bottom: 8px;
    }

    /* 右侧面板优化 */
    .right-panel {
        max-height: calc(100vh - 120px);
        overflow: hidden;
    }

    .calendar-panel-card {
        max-height: 40vh; /* 改为视口高度的40% */
        min-height: 30vh; /* 确保日期完全显示 */
    }

    /* alert-info-card 移除高度限制，让它自适应到底部 */

    .latest-info-list {
        /* 移除高度限制，让它自适应填满空间 */
        flex: 1;
        overflow-y: auto; /* 允许滚动 */
    }

    /* 图表区域优化 */
    .chart-container {
        height: 150px; /* 缩小图表高度 */
    }

    /* efficiency-chart-container 已在精确媒体查询中定义 */

    /* 生产统计优化 */
    .production-stats {
        max-height: 200px; /* 限制生产统计高度 */
    }

    /* 设备卡片内容优化 */
    .metric-value {
        font-size: 1rem; /* 缩小指标数值字体 */
    }

    .metric-label {
        font-size: 0.7rem; /* 缩小指标标签字体 */
    }

    .device-name {
        font-size: 1rem; /* 缩小设备名称字体 */
    }



    /* 控制按钮优化 */
    .line-select-small {
        padding: 6px 10px;
        font-size: 0.85rem;
        min-width: 120px; /* 缩小选择器宽度 */
    }

    .refresh-btn-small {
        min-width: 32px;
        height: 32px; /* 缩小刷新按钮 */
    }

    .line-selector-label {
        font-size: 0.85rem; /* 缩小标签字体 */
    }
}

/* 2K屏幕及以上分辨率优化 */
@media screen and (min-width: 1921px) {
    .dual-panel-container {
        grid-template-columns: 1fr 1fr 380px;
        gap: 25px;
    }

    .main-content {
        gap: 25px;
        padding: 25px;
        padding-bottom: 20px; /* 保持底部20px距离 */
        max-height: calc(100vh - 100px);
    }

    .panel-card {
        padding: 20px;
        margin-bottom: 20px;
    }

    .device-card-new {
        min-height: 200px; /* 增大设备卡片高度 */
        padding: 18px;
    }

    .device-grid {
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr)); /* 增大设备卡片最小宽度 */
        gap: 20px;
        max-height: calc(100vh - 400px);
    }

    .stats-cards {
        grid-template-columns: repeat(4, 1fr);
        gap: 25px;
        margin-bottom: 25px;
    }

    .stat-card {
        padding: 20px;
    }

    .stat-number {
        font-size: 2.8rem; /* 增大数字字体 */
    }

    .stat-card h4 {
        font-size: 1.1rem; /* 增大标题字体 */
    }

    .main-title {
        font-size: 2.4rem; /* 增大主标题 */
    }

    .header {
        padding: 18px 30px; /* 增大头部内边距 */
    }

    .current-time {
        padding: 8px 15px;
        font-size: 0.9rem; /* 增大字体 */
    }



    .panel-title {
        font-size: 1.3rem; /* 增大面板标题 */
        margin-bottom: 15px;
    }

    /* 右侧面板优化 */
    .right-panel {
        max-height: calc(100vh - 140px);
    }

    .calendar-panel-card {
        max-height: 50vh; /* 改为视口高度的50% */
        min-height: 40vh; /* 确保日期完全显示 */
    }

    /* alert-info-card 移除高度限制，让它自适应到底部 */

    .latest-info-list {
        /* 移除高度限制，让它自适应填满空间 */
        flex: 1;
        overflow-y: auto; /* 允许滚动 */
    }

    /* 图表区域优化 */
    .chart-container {
        height: 220px; /* 增大图表高度 */
    }



    /* 生产统计优化 */
    .production-stats {
        max-height: 300px; /* 增大生产统计高度 */
    }

    /* 设备卡片内容优化 */
    .metric-value {
        font-size: 1.4rem; /* 增大指标数值字体 */
    }

    .metric-label {
        font-size: 0.85rem; /* 增大指标标签字体 */
    }

    .device-name {
        font-size: 1.2rem; /* 增大设备名称字体 */
    }



    /* 控制按钮优化 */
    .line-select-small {
        padding: 10px 15px;
        font-size: 1rem;
        min-width: 160px; /* 增大选择器宽度 */
    }

    .refresh-btn-small {
        min-width: 40px;
        height: 40px; /* 增大刷新按钮 */
    }

    .line-selector-label {
        font-size: 1rem; /* 增大标签字体 */
    }
}

/* 超小屏幕优化 (小于1600px) */
@media screen and (max-width: 1599px) {
    .dual-panel-container {
        grid-template-columns: 1fr 1fr 260px;
        gap: 10px;
    }

    .main-content {
        gap: 10px;
        padding: 10px;
        padding-bottom: 20px; /* 保持底部20px距离 */
    }

    .stats-cards {
        grid-template-columns: repeat(2, 1fr); /* 改为2列 */
        gap: 8px;
    }

    .device-grid {
        grid-template-columns: repeat(auto-fit, minmax(160px, 1fr)); /* 更小的设备卡片 */
        gap: 10px;
    }

    .device-card-new {
        min-height: 120px;
        padding: 10px;
    }

    .main-title {
        font-size: 1.4rem;
    }

    .panel-title {
        font-size: 0.9rem;
    }



    .stats-container {
        max-width: 160px;
        gap: 6px;
    }

    .stat-item {
        min-height: 35px;
        padding: 4px;
    }

    .stat-label {
        font-size: 0.65rem;
    }

    .stat-value {
        font-size: 0.8rem;
    }

    .calendar-panel-card {
        max-height: 35vh; /* 改为视口高度的35% */
        min-height: 25vh; /* 确保日期完全显示 */
    }

    /* alert-info-card 移除高度限制，让它自适应到底部 */
}

/* 确保弹窗在所有分辨率下都能正常显示 */
@media screen and (max-height: 800px) {
    .modal-content {
        margin: 2% auto;
        max-height: 95vh;
        width: 95%;
        max-width: 1300px;
    }

    .modal-body {
        padding: 15px;
    }

    .alarm-stats {
        grid-template-columns: repeat(3, 1fr);
        gap: 10px;
    }

    .stat-item-modal {
        padding: 10px;
    }

    .stat-item-modal .value {
        font-size: 1.4rem;
    }
}

/* 生产统计新样式 - 设备列表 */
.production-date {
    text-align: center;
    color: #4fd1c7;
    font-size: 1.1rem;
    font-weight: bold;
    margin-bottom: 15px;
    padding: 10px;
    background: rgba(79, 209, 199, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(79, 209, 199, 0.3);
}

.device-production-item {
    background: rgba(45, 55, 72, 0.6);
    border: 1px solid rgba(79, 209, 199, 0.2);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.device-production-item:hover {
    background: rgba(79, 209, 199, 0.1);
    border-color: #4fd1c7;
    transform: translateX(5px);
}

.device-info {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 6px;
}

.device-name {
    color: #ffffff;
    font-weight: 500;
    font-size: 0.95rem;
}

.device-id {
    color: #a0aec0;
    font-size: 0.85rem;
}

.production-data {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
}

.production-count {
    color: #48bb78;
    font-weight: bold;
    font-size: 0.9rem;
}

.production-oee {
    color: #ed8936;
    font-weight: bold;
    font-size: 0.9rem;
}

.production-summary {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid rgba(79, 209, 199, 0.3);
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 0;
    border-bottom: 1px solid rgba(79, 209, 199, 0.1);
}

.summary-item:last-child {
    border-bottom: none;
}

.summary-label {
    color: #a0aec0;
    font-size: 0.85rem;
}

.summary-value {
    color: #4fd1c7;
    font-weight: bold;
    font-size: 1rem;
    text-shadow: 0 0 6px rgba(79, 209, 199, 0.5);
}

/* 生产统计简化样式 - 一行显示 */
.device-production-simple {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    margin-bottom: 4px;
    background: rgba(45, 55, 72, 0.4);
    border: 1px solid rgba(79, 209, 199, 0.2);
    border-radius: 6px;
    transition: all 0.2s ease;
}

.device-production-simple:hover {
    background: rgba(79, 209, 199, 0.1);
    border-color: #4fd1c7;
}

.device-name-simple {
    color: #ffffff;
    font-size: 0.9rem;
    font-weight: 500;
    flex: 1;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

.production-count-simple {
    color: #48bb78;
    font-weight: bold;
    font-size: 1rem;
    text-shadow: 0 0 4px rgba(72, 187, 120, 0.5);
    min-width: 40px;
    text-align: right;
}

/* 优化日期标题样式 */
.production-date {
    text-align: center;
    color: #4fd1c7;
    font-size: 1rem;
    font-weight: bold;
    margin-bottom: 12px;
    padding: 8px;
    background: rgba(79, 209, 199, 0.1);
    border-radius: 6px;
    border: 1px solid rgba(79, 209, 199, 0.3);
}

/* 生产统计区域自动拉伸 */
#production-stats {
    flex: 1; /* 自动拉伸填满可用空间 */
    min-height: 200px; /* 最小高度 */
    max-height: 400px; /* 最大高度限制 */
    overflow: hidden; /* 隐藏滚动条 */
    position: relative;
    display: flex;
    flex-direction: column;
}

/* 生产统计内容容器 - 自动滚动 */
.production-stats-content {
    animation: autoScroll 30s linear infinite;
    padding-right: 10px; /* 避免内容贴边 */
}

/* 自动滚动动画 */
@keyframes autoScroll {
    0% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-50%);
    }
    100% {
        transform: translateY(0);
    }
}

/* 生产统计头部 */
.production-header {
    margin-bottom: 10px;
}

/* 生产统计容器 - 固定表头设计 */
.production-stats-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    max-height: 400px; /* 限制最大高度 */
}

/* 固定表头区域 */
.production-stats-header {
    flex-shrink: 0; /* 不允许收缩 */
    position: sticky;
    top: 0;
    z-index: 10;
    background: rgba(26, 32, 44, 0.95); /* 半透明背景 */
    backdrop-filter: blur(10px); /* 毛玻璃效果 */
    border-bottom: 2px solid rgba(79, 209, 199, 0.6);
}

/* 固定表头样式 */
.shift-header-fixed {
    display: table !important;
    width: 100% !important;
    table-layout: fixed !important;
    border-collapse: separate !important;
    border-spacing: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    box-sizing: border-box !important;
    background: linear-gradient(135deg, rgba(79, 209, 199, 0.25), rgba(79, 209, 199, 0.15)) !important;
    border: 2px solid rgba(79, 209, 199, 0.6) !important;
    border-radius: 8px !important;
    font-weight: 600 !important;
    box-shadow: 0 2px 8px rgba(79, 209, 199, 0.2) !important;
}

/* 固定表头单元格 */
.shift-header-fixed > span {
    display: table-cell !important;
    vertical-align: middle !important;
    padding: 12px 8px !important;
    margin: 0 !important;
    text-align: center !important;
    font-weight: 700 !important;
    line-height: 1.4 !important;
    color: #4fd1c7 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

/* 固定表头列宽 */
.shift-header-fixed .device-col {
    width: 40% !important;
    text-align: left !important;
    padding-left: 16px !important;
}

.shift-header-fixed .shift-col:nth-child(2) {
    width: 20% !important;
}

.shift-header-fixed .shift-col:nth-child(3) {
    width: 20% !important;
}

.shift-header-fixed .total-col {
    width: 20% !important;
}

/* 数据区域 - 可滚动 */
.production-stats {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    max-height: 320px; /* 限制数据区域高度 */
    padding-right: 4px; /* 为滚动条留出空间 */
}

/* 自定义滚动条样式 */
.production-stats::-webkit-scrollbar {
    width: 6px;
}

.production-stats::-webkit-scrollbar-track {
    background: rgba(79, 209, 199, 0.1);
    border-radius: 3px;
}

.production-stats::-webkit-scrollbar-thumb {
    background: rgba(79, 209, 199, 0.5);
    border-radius: 3px;
}

.production-stats::-webkit-scrollbar-thumb:hover {
    background: rgba(79, 209, 199, 0.7);
}

.data-source-info {
    display: none; /* 隐藏数据源信息区域 */
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    padding: 8px 12px;
    background: rgba(79, 209, 199, 0.1);
    border: 1px solid rgba(79, 209, 199, 0.3);
    border-radius: 6px;
}

.data-source-label {
    color: #4fd1c7;
    font-size: 0.9rem;
    font-weight: 600;
}

.toggle-data-btn {
    background: linear-gradient(145deg, #4fd1c7, #38b2ac);
    border: none;
    border-radius: 6px;
    color: #ffffff;
    padding: 6px 12px;
    font-size: 0.8rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(79, 209, 199, 0.3);
}

.toggle-data-btn:hover {
    background: linear-gradient(145deg, #38b2ac, #2c7a7b);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(79, 209, 199, 0.4);
}

.toggle-data-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(79, 209, 199, 0.3);
}

/* 班次统计样式 */
.shift-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 8px;
    padding: 10px 12px;
    background: rgba(79, 209, 199, 0.15);
    border: 1px solid rgba(79, 209, 199, 0.4);
    border-radius: 6px;
    margin-bottom: 8px;
    font-weight: bold;
    font-size: 0.9rem;
    color: #4fd1c7;
    align-items: center;
}

.device-shift-item {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 8px;
    align-items: center;
    padding: 10px 12px;
    margin-bottom: 4px;
    background: rgba(45, 55, 72, 0.4);
    border: 1px solid rgba(79, 209, 199, 0.2);
    border-radius: 6px;
    transition: all 0.2s ease;
    font-size: 0.85rem;
}

.device-shift-item:hover {
    background: rgba(79, 209, 199, 0.1);
    border-color: #4fd1c7;
}

/* 表头列样式 - 确保与数据行对齐 */
.shift-header .device-col {
    text-align: left;
    font-weight: bold;
    padding: 0;
    margin: 0;
}

.shift-header .shift-col,
.shift-header .total-col {
    text-align: center;
    font-weight: bold;
    padding: 0;
    margin: 0;
}

/* 数据行列样式 - 确保与表头对齐 */
.device-name-shift {
    color: #ffffff;
    font-weight: 500;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    text-align: left;
    padding: 0;
    margin: 0;
    display: flex;
    align-items: center;
}

.day-shift {
    color: #ed8936;
    font-weight: bold;
    text-align: center;
    text-shadow: 0 0 4px rgba(237, 137, 54, 0.5);
    padding: 0;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.night-shift {
    color: #9f7aea;
    font-weight: bold;
    text-align: center;
    text-shadow: 0 0 4px rgba(159, 122, 234, 0.5);
    padding: 0;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.total-shift {
    color: #48bb78;
    font-weight: bold;
    text-align: center;
    text-shadow: 0 0 4px rgba(72, 187, 120, 0.5);
    padding: 0;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.device-col, .shift-col, .total-col {
    text-align: center;
}

.device-col {
    text-align: left;
}

.shift-summary {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid rgba(79, 209, 199, 0.3);
}

.shift-summary .summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 0;
    border-bottom: 1px solid rgba(79, 209, 199, 0.1);
    font-size: 0.85rem;
}

.shift-summary .summary-item:last-child {
    border-bottom: none;
}

.shift-summary .summary-label {
    color: #a0aec0;
}

.shift-summary .summary-value {
    color: #4fd1c7;
    font-weight: bold;
    text-shadow: 0 0 6px rgba(79, 209, 199, 0.5);
}

/* 紧凑型生产线选择器样式 */
.line-selector-compact {
    display: flex;
    align-items: center;
    gap: 8px;
}

.line-selector-label {
    color: #4fd1c7;
    font-size: 0.9rem;
    font-weight: 500;
    text-shadow: 0 0 6px rgba(79, 209, 199, 0.5);
    white-space: nowrap;
}

.line-select-small {
    background: linear-gradient(145deg, #2d3748, #4a5568);
    border: 1px solid rgba(79, 209, 199, 0.5);
    border-radius: 6px;
    color: #ffffff;
    padding: 8px 12px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 140px;
}

.line-select-small:hover {
    border-color: #4fd1c7;
    box-shadow: 0 0 10px rgba(79, 209, 199, 0.4);
}

.line-select-small:focus {
    outline: none;
    border-color: #4fd1c7;
    box-shadow: 0 0 15px rgba(79, 209, 199, 0.6);
}

.line-select-small option {
    background: #2d3748;
    color: #ffffff;
    padding: 8px;
}

.refresh-btn-small {
    background: linear-gradient(145deg, #4fd1c7, #38b2ac);
    border: none;
    border-radius: 8px;
    color: #ffffff;
    padding: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow:
        0 4px 12px rgba(79, 209, 199, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    min-width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.refresh-btn-small::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.5s ease;
    opacity: 0;
}

.refresh-btn-small:hover::before {
    opacity: 1;
    animation: shine 0.6s ease-in-out;
}

.refresh-btn-small:hover {
    background: linear-gradient(145deg, #38b2ac, #2c7a7b);
    box-shadow:
        0 6px 20px rgba(79, 209, 199, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        0 0 20px rgba(79, 209, 199, 0.4);
    transform: translateY(-2px) scale(1.05);
}

/* 添加旋转动画效果 */
.refresh-btn-small:active {
    animation: spin 0.6s ease-in-out;
}

@keyframes spin {
    0% { transform: translateY(0) scale(0.98) rotate(0deg); }
    50% { transform: translateY(-1px) scale(1.02) rotate(180deg); }
    100% { transform: translateY(0) scale(0.98) rotate(360deg); }
}

/* 最新信息列表 */
.latest-info-list {
    flex: 1; /* 自适应填满剩余空间 */
    overflow-y: auto; /* 允许滚动 */
}

.info-item {
    background: rgba(45, 55, 72, 0.6);
    border: 1px solid rgba(79, 209, 199, 0.2);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 1vh; /* 改为视口高度的1% */
    transition: all 0.3s ease;
}

.info-item:hover {
    background: rgba(79, 209, 199, 0.1);
    border-color: #4fd1c7;
    transform: translateX(5px);
}

.info-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.info-device {
    color: #4fd1c7;
    font-weight: bold;
    font-size: 1rem;
}

.info-time {
    color: #a0aec0;
    font-size: 0.85rem;
}

.info-message {
    color: #ffffff;
    line-height: 1.4;
    font-size: 0.95rem;
}

/* 单行报警信息样式 */
.info-item-inline {
    background: rgba(45, 55, 72, 0.6);
    border: 1px solid rgba(79, 209, 199, 0.2);
    border-radius: 6px;
    padding: 8px 12px;
    margin-bottom: 0.6vh; /* 改为视口高度的0.6% */
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.85rem;
}

.info-item-inline:hover {
    background: rgba(79, 209, 199, 0.1);
    border-color: #4fd1c7;
    transform: translateX(3px);
}

.info-time-inline {
    color: #a0aec0;
    font-size: 0.8rem;
    white-space: nowrap;
    min-width: 50px;
    flex-shrink: 0;
}

.info-message-inline {
    color: #ffffff;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

@keyframes modalFadeIn {
    from { opacity: 0; transform: translateY(-50px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 报警表格样式 */
.alarm-table-container {
    overflow-x: auto;
    border-radius: 8px;
    border: 1px solid rgba(79, 209, 199, 0.2);
    margin: 10px 0;
    background: rgba(26, 35, 50, 0.6);
}

.alarm-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.85rem;
    table-layout: fixed;
}

.alarm-table thead {
    background: rgba(79, 209, 199, 0.15);
    position: sticky;
    top: 0;
    z-index: 10;
}

.alarm-table th {
    padding: 12px 10px;
    text-align: left;
    color: #4fd1c7;
    font-weight: bold;
    border-bottom: 2px solid rgba(79, 209, 199, 0.3);
    white-space: nowrap;
    font-size: 0.9rem;
}

.alarm-table td {
    padding: 10px;
    border-bottom: 1px solid rgba(79, 209, 199, 0.1);
    vertical-align: top;
    overflow: hidden;
    word-wrap: break-word;
}

.alarm-row {
    transition: all 0.2s ease;
}

.alarm-row:hover {
    background: rgba(79, 209, 199, 0.1);
}

/* 表格列宽度设置 */
.device-col {
    width: 18%;
    min-width: 140px;
}

.content-col {
    width: 32%;
    min-width: 200px;
}

.time-col {
    width: 22%;
    min-width: 140px;
}

.status-col {
    width: 12%;
    min-width: 80px;
    text-align: center;
}

.duration-col {
    width: 10%;
    min-width: 70px;
    text-align: center;
}

.severity-col {
    width: 6%;
    min-width: 50px;
    text-align: center;
}

/* 表格内容样式 */
.device-name-table {
    color: #4fd1c7;
    font-weight: bold;
    font-size: 0.9rem;
    margin-bottom: 2px;
}

.create-time-table {
    color: #a0aec0;
    font-size: 0.75rem;
}

.alarm-content-table {
    color: #ffffff;
    line-height: 1.4;
    word-wrap: break-word;
}

.time-display-table {
    color: #e2e8f0;
    font-size: 0.85rem;
    line-height: 1.3;
}

.duration-display-table {
    color: #ed8936;
    font-weight: bold;
    font-size: 0.85rem;
}

.alarm-status-table {
    display: inline-flex;
    align-items: center;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: bold;
    white-space: nowrap;
}

.alarm-status-table.开始 {
    background: rgba(245, 101, 101, 0.2);
    color: #f56565;
    border: 1px solid rgba(245, 101, 101, 0.4);
}

.alarm-status-table.持续中 {
    background: rgba(237, 137, 54, 0.2);
    color: #ed8936;
    border: 1px solid rgba(237, 137, 54, 0.4);
    animation: pulse-continue 2s infinite;
}

.alarm-status-table.结束 {
    background: rgba(72, 187, 120, 0.2);
    color: #48bb78;
    border: 1px solid rgba(72, 187, 120, 0.4);
}

.alarm-severity-table {
    display: inline-block;
    padding: 3px 6px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: bold;
    white-space: nowrap;
}

.alarm-severity-table.high {
    background: linear-gradient(145deg, #f56565, #e53e3e);
    color: #ffffff;
}

.alarm-severity-table.medium {
    background: linear-gradient(145deg, #ed8936, #dd6b20);
    color: #ffffff;
}

.alarm-severity-table.low {
    background: linear-gradient(145deg, #ecc94b, #d69e2e);
    color: #000000;
}

@keyframes pulse-continue {
    0%, 100% { 
        opacity: 1; 
        transform: scale(1);
    }
    50% { 
        opacity: 0.7; 
        transform: scale(1.05);
    }
}

.total-shift {
    color: #48bb78;
    font-weight: bold;
    text-align: center;
    text-shadow: 0 0 4px rgba(72, 187, 120, 0.5);
}

.shift-summary {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid rgba(79, 209, 199, 0.3);
}

/* 生产统计表格对齐修复 - 数据行使用table布局与固定表头对齐 */
.production-stats .device-shift-item {
    display: table !important;
    width: 100% !important;
    table-layout: fixed !important;
    border-collapse: separate !important;
    border-spacing: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    box-sizing: border-box !important;
    background: linear-gradient(135deg, rgba(45, 55, 72, 0.8), rgba(45, 55, 72, 0.6)) !important;
    border: 1px solid rgba(79, 209, 199, 0.3) !important;
    border-radius: 6px !important;
    margin-bottom: 3px !important;
    transition: all 0.3s ease !important;
}

.production-stats .device-shift-item {
    background: rgba(45, 55, 72, 0.4) !important;
    border: 1px solid rgba(79, 209, 199, 0.2) !important;
    border-radius: 6px !important;
    margin-bottom: 4px !important;
}

/* 统一所有列的基础样式 */
.production-stats .device-shift-item > span {
    display: block !important;
    padding: 0 !important;
    margin: 0 !important;
    text-align: center !important;
}

/* 第一列（设备名）- 固定宽度，左对齐 */
.production-stats .device-col,
.production-stats .device-name-shift {
    flex: 0 0 120px !important;
    text-align: left !important;
    color: #ffffff !important;
}

/* 第二列（白班）- 等宽分布 */
.production-stats .shift-col:nth-child(2),
.production-stats .day-shift {
    flex: 1 !important;
    color: #ed8936 !important;
    font-weight: bold !important;
    margin-left: -20px !important;
}

/* 第三列（夜班）- 等宽分布 */
.production-stats .shift-col:nth-child(3),
.production-stats .night-shift {
    flex: 1 !important;
    color: #9f7aea !important;
    font-weight: bold !important;
    margin-left: -3px !important;
}

/* 第四列（总数）- 等宽分布 */
.production-stats .total-col,
.production-stats .total-shift {
    flex: 1 !important;
    color: #48bb78 !important;
    font-weight: bold !important;
    margin-left: -8px !important;
}

/* 移除表头文字颜色样式，现在使用固定表头 */

/* 悬停效果 */
.production-stats .device-shift-item:hover {
    background: rgba(79, 209, 199, 0.1) !important;
    border-color: #4fd1c7 !important;
}

/* ==================================================================
   🎨 生产统计表格 - 1920*1080专用美观设计
   ================================================================== */

/* 重置原有样式，使用现代化表格设计 */
.production-stats .device-shift-item {
    display: table !important;
    width: 100% !important;
    table-layout: fixed !important;
    border-collapse: separate !important;
    border-spacing: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    box-sizing: border-box !important;
}

/* 移除原来的动态表头样式，现在使用固定表头 */

.production-stats .device-shift-item {
    background: linear-gradient(135deg, rgba(45, 55, 72, 0.8), rgba(45, 55, 72, 0.6)) !important;
    border: 1px solid rgba(79, 209, 199, 0.3) !important;
    border-radius: 6px !important;
    margin-bottom: 3px !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    font-size: 0.9rem !important;
    backdrop-filter: blur(10px) !important;
}

.production-stats .device-shift-item:hover {
    background: linear-gradient(135deg, rgba(79, 209, 199, 0.15), rgba(79, 209, 199, 0.05)) !important;
    border-color: #4fd1c7 !important;
    transform: translateX(2px) scale(1.01) !important;
    box-shadow: 0 4px 12px rgba(79, 209, 199, 0.3) !important;
}

/* 表格单元格样式 */
.production-stats .device-shift-item > span {
    display: table-cell !important;
    vertical-align: middle !important;
    padding: 12px 8px !important;
    margin: 0 !important;
    text-align: center !important;
    font-weight: 500 !important;
    line-height: 1.4 !important;
}

/* 精确列宽定义 - 与固定表头保持一致 */
.production-stats .device-name-shift {
    width: 40% !important;
    text-align: left !important;
    padding-left: 16px !important;
    font-weight: 600 !important;
    color: #ffffff !important;
}

.production-stats .day-shift {
    width: 20% !important;
    color: #ed8936 !important;
    font-weight: bold !important;
    text-shadow: 0 0 4px rgba(237, 137, 54, 0.5) !important;
}

.production-stats .night-shift {
    width: 20% !important;
    color: #9f7aea !important;
    font-weight: bold !important;
    text-shadow: 0 0 4px rgba(159, 122, 234, 0.5) !important;
}

.production-stats .total-shift {
    width: 20% !important;
    color: #48bb78 !important;
    font-weight: bold !important;
    text-shadow: 0 0 4px rgba(72, 187, 120, 0.5) !important;
}

/* 移除表头美化样式，现在使用固定表头 */

/* 数据行美化 */
.production-stats .device-name-shift {
    color: #ffffff !important;
    font-weight: 600 !important;
    text-overflow: ellipsis !important;
    overflow: hidden !important;
    white-space: nowrap !important;
}

.production-stats .day-shift {
    color: #d4a574 !important;
    font-weight: 700 !important;
    text-shadow: 0 0 4px rgba(212, 165, 116, 0.4) !important;
    background: rgba(212, 165, 116, 0.08) !important;
    border-radius: 4px !important;
    margin: 4px !important;
}

.production-stats .night-shift {
    color: #9c88b8 !important;
    font-weight: 700 !important;
    text-shadow: 0 0 4px rgba(156, 136, 184, 0.4) !important;
    background: rgba(156, 136, 184, 0.08) !important;
    border-radius: 4px !important;
    margin: 4px !important;
}

.production-stats .total-shift {
    color: #7db383 !important;
    font-weight: 700 !important;
    text-shadow: 0 0 4px rgba(125, 179, 131, 0.4) !important;
    background: rgba(125, 179, 131, 0.08) !important;
    border-radius: 4px !important;
    margin: 4px !important;
}

/* 响应式动画效果 */
@keyframes statsGlow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(79, 209, 199, 0.3);
    }
    50% {
        box-shadow: 0 0 15px rgba(79, 209, 199, 0.6);
    }
}

/* 移除表头动画，现在使用固定表头 */

/* 数字动画效果 */
.production-stats .day-shift:hover,
.production-stats .night-shift:hover,
.production-stats .total-shift:hover {
    transform: scale(1.1) !important;
    transition: transform 0.2s ease !important;
}

/* 滚动容器优化 */
.production-stats-content {
    max-height: 280px !important;
    overflow-y: auto !important;
    padding-right: 4px !important;
}

.production-stats-content::-webkit-scrollbar {
    width: 6px !important;
}

.production-stats-content::-webkit-scrollbar-track {
    background: rgba(79, 209, 199, 0.1) !important;
    border-radius: 3px !important;
}

.production-stats-content::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #4fd1c7, #38b2ac) !important;
    border-radius: 3px !important;
}

.production-stats-content::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #38b2ac, #2c7a7b) !important;
}

/* 滚动容器优化 - 移除滚动，直接显示所有数据 */
.production-stats-content {
    max-height: none !important;
    overflow: visible !important;
    padding-right: 0 !important;
}

/* 移除滚动条样式 */
.production-stats-content::-webkit-scrollbar {
    display: none !important;
}

.production-stats-content::-webkit-scrollbar-track {
    display: none !important;
}

.production-stats-content::-webkit-scrollbar-thumb {
    display: none !important;
}

.production-stats-content::-webkit-scrollbar-thumb:hover {
    display: none !important;
}

/* 全局防止溢出规则 */
html, body {
    max-width: 100vw;
    overflow-x: hidden;
}

/* 确保所有容器不超出视口宽度 */
.dashboard-container,
.main-content,
.center-panel,
.right-panel {
    max-width: 100%;
    box-sizing: border-box;
}

/* 表格容器防止水平溢出 */
.device-table-container,
.device-status-table-container {
    max-width: 100%;
    box-sizing: border-box;
}

/* 表格自适应滚动条样式 */
.device-table-container::-webkit-scrollbar,
.device-status-table-container::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.device-table-container::-webkit-scrollbar-track,
.device-status-table-container::-webkit-scrollbar-track {
    background: rgba(45, 55, 72, 0.5);
    border-radius: 4px;
}

.device-table-container::-webkit-scrollbar-thumb,
.device-status-table-container::-webkit-scrollbar-thumb {
    background: rgba(79, 209, 199, 0.6);
    border-radius: 4px;
    transition: background 0.3s ease;
}

.device-table-container::-webkit-scrollbar-thumb:hover,
.device-status-table-container::-webkit-scrollbar-thumb:hover {
    background: rgba(79, 209, 199, 0.8);
}

/* 底部实时状态指示器样式 */
#realtime-indicator {
    z-index: 1000;
    box-sizing: border-box;
}

/* 确保主内容区域为底部状态栏留出空间 */
.main-content {
    padding-bottom: 20px !important;
}

/* 确保表格在小屏幕上的响应式行为 */
@media screen and (max-width: 1200px) {
    .device-table {
        /* 移除min-width，让表格完全适应容器 */
    }

    .device-status-table {
        /* 移除min-width，让表格完全适应容器 */
    }

    .device-table th,
    .device-table td {
        padding: 8px 6px;
        font-size: 13px;
    }

    .device-status-table th,
    .device-status-table td {
        padding: 10px 8px;
        font-size: 13px;
    }
}

/* 极小屏幕优化 - 防止溢出 */
@media screen and (max-width: 1024px) {
    .main-content {
        flex-direction: column;
        gap: 15px;
        padding: 15px;
        padding-bottom: 20px; /* 保持底部20px距离 */
    }

    .dual-panel-container {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto;
        gap: 15px;
    }

    .left-device-panel,
    .right-device-panel,
    .right-panel {
        width: 100%;
        max-width: 100%;
    }

    .device-table {
        /* 移除min-width，让表格完全适应容器 */
        font-size: 12px;
    }

    .device-status-table {
        /* 移除min-width，让表格完全适应容器 */
        font-size: 12px;
    }

    .device-table th,
    .device-table td {
        padding: 6px 4px;
    }

    .device-status-table th,
    .device-status-table td {
        padding: 8px 6px;
    }
}