from flask import Flask, request, jsonify, render_template_string, session, redirect, url_for, send_from_directory
from flask_cors import CORS
import pyodbc
import hashlib
import uuid
from datetime import datetime, timedelta
import os
from functools import wraps

app = Flask(__name__, static_folder='.', static_url_path='')
app.secret_key = 'your-secret-key-change-this'  # 请更改为安全的密钥
CORS(app)

# 数据库配置 - Windows认证
DB_CONFIG = {
    'server': 'localhost',
    'database': 'DeviceDataSource',
    'driver': '{ODBC Driver 17 for SQL Server}',
    'trusted_connection': 'yes'
}

# 尝试不同的ODBC驱动
DRIVERS = [
    '{ODBC Driver 17 for SQL Server}',
    '{ODBC Driver 13 for SQL Server}',
    '{SQL Server Native Client 11.0}',
    '{SQL Server}',
]

def get_db_connection():
    """获取数据库连接 - Windows认证"""
    for driver in DRIVERS:
        try:
            connection_string = f"""
                DRIVER={driver};
                SERVER={DB_CONFIG['server']};
                DATABASE={DB_CONFIG['database']};
                Trusted_Connection={DB_CONFIG['trusted_connection']};
                TrustServerCertificate=yes;
            """
            connection = pyodbc.connect(connection_string)
            return connection
        except Exception:
            continue
    return None

def login_required(f):
    """登录验证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

def admin_required(f):
    """管理员权限验证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        if session.get('is_admin') != 'Y':
            return jsonify({'error': '需要管理员权限'}), 403
        return f(*args, **kwargs)
    return decorated_function

@app.route('/')
def index():
    """首页重定向到登录页面"""
    if 'user_id' in session:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login')
def login():
    """登录页面"""
    return render_template_string(LOGIN_TEMPLATE)

@app.route('/api/login', methods=['POST'])
def api_login():
    """登录API"""
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')

        if not username or not password:
            return jsonify({'success': False, 'message': '用户名和密码不能为空'})

        conn = get_db_connection()
        if not conn:
            return jsonify({'success': False, 'message': '数据库连接失败'})

        cursor = conn.cursor()
        cursor.execute("""
            SELECT dataid, username, userpwd, isadmin
            FROM userinfotable
            WHERE username = ?
        """, username)

        user = cursor.fetchone()
        conn.close()

        if not user:
            return jsonify({'success': False, 'message': '用户名不存在'})

        # 验证密码（这里假设密码是明文存储，实际应该使用哈希）
        if user[2] != password:
            return jsonify({'success': False, 'message': '密码错误'})

        # 设置session
        session['user_id'] = user[0]
        session['username'] = user[1]
        session['is_admin'] = user[3]

        return jsonify({
            'success': True,
            'message': '登录成功',
            'is_admin': user[3] == 'Y'
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'登录失败: {str(e)}'})

@app.route('/logout')
def logout():
    """退出登录"""
    session.clear()
    return redirect('/')

@app.route('/dashboard')
@login_required
def dashboard():
    """后台管理主页"""
    return render_template_string(DASHBOARD_TEMPLATE)

@app.route('/api/current-user')
@login_required
def get_current_user():
    """获取当前用户信息API"""
    return jsonify({
        'success': True,
        'user': session.get('username'),
        'is_admin': session.get('is_admin') == 'Y'
    })

@app.route('/api/change-password', methods=['POST'])
@login_required
def change_password():
    """修改密码API"""
    try:
        data = request.get_json()
        old_password = data.get('old_password')
        new_password = data.get('new_password')

        if not old_password or not new_password:
            return jsonify({'success': False, 'message': '旧密码和新密码不能为空'})

        conn = get_db_connection()
        if not conn:
            return jsonify({'success': False, 'message': '数据库连接失败'})

        cursor = conn.cursor()

        # 验证旧密码
        cursor.execute("""
            SELECT userpwd FROM userinfotable WHERE dataid = ?
        """, session['user_id'])

        current_pwd = cursor.fetchone()
        if not current_pwd or current_pwd[0] != old_password:
            conn.close()
            return jsonify({'success': False, 'message': '旧密码错误'})

        # 更新密码
        cursor.execute("""
            UPDATE userinfotable SET userpwd = ? WHERE dataid = ?
        """, new_password, session['user_id'])

        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': '密码修改成功'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'密码修改失败: {str(e)}'})

@app.route('/api/users', methods=['GET'])
@admin_required
def get_users():
    """获取用户列表API（仅管理员）"""
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({'success': False, 'message': '数据库连接失败'})

        cursor = conn.cursor()
        cursor.execute("""
            SELECT dataid, username, isadmin, createtime
            FROM userinfotable
            ORDER BY createtime DESC
        """)

        users = []
        for row in cursor.fetchall():
            users.append({
                'dataid': row[0],
                'username': row[1],
                'isadmin': row[2],
                'createtime': row[3].strftime('%Y-%m-%d %H:%M:%S') if row[3] else ''
            })

        conn.close()
        return jsonify({'success': True, 'users': users})

    except Exception as e:
        return jsonify({'success': False, 'message': f'获取用户列表失败: {str(e)}'})

@app.route('/api/users', methods=['POST'])
@admin_required
def add_user():
    """添加用户API（仅管理员）"""
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        is_admin = data.get('is_admin', 'N')

        if not username or not password:
            return jsonify({'success': False, 'message': '用户名和密码不能为空'})

        conn = get_db_connection()
        if not conn:
            return jsonify({'success': False, 'message': '数据库连接失败'})

        cursor = conn.cursor()

        # 检查用户名是否已存在
        cursor.execute("SELECT username FROM userinfotable WHERE username = ?", username)
        if cursor.fetchone():
            conn.close()
            return jsonify({'success': False, 'message': '用户名已存在'})

        # 生成新的dataid
        dataid = str(uuid.uuid4())

        # 插入新用户
        cursor.execute("""
            INSERT INTO userinfotable (dataid, username, userpwd, isadmin, createtime)
            VALUES (?, ?, ?, ?, ?)
        """, dataid, username, password, is_admin, datetime.now())

        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': '用户添加成功'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'添加用户失败: {str(e)}'})

@app.route('/api/users/<user_id>/reset-password', methods=['POST'])
@admin_required
def reset_user_password(user_id):
    """重置用户密码API（仅管理员）"""
    try:
        data = request.get_json()
        new_password = data.get('new_password')

        if not new_password:
            return jsonify({'success': False, 'message': '新密码不能为空'})

        conn = get_db_connection()
        if not conn:
            return jsonify({'success': False, 'message': '数据库连接失败'})

        cursor = conn.cursor()
        cursor.execute("""
            UPDATE userinfotable SET userpwd = ? WHERE dataid = ?
        """, new_password, user_id)

        if cursor.rowcount == 0:
            conn.close()
            return jsonify({'success': False, 'message': '用户不存在'})

        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': '密码重置成功'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'密码重置失败: {str(e)}'})

# 数据导出API
@app.route('/api/export/production-data', methods=['GET'])
@login_required
def export_production_data():
    """导出生产数据API"""
    try:
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        conn = get_db_connection()
        if not conn:
            return jsonify({'success': False, 'message': '数据库连接失败'})

        cursor = conn.cursor()

        # 构建查询条件
        where_clause = ""
        params = []
        if start_date and end_date:
            where_clause = "WHERE productdate BETWEEN ? AND ?"
            params = [start_date, end_date]

        # 查询生产数据
        query = f"""
            SELECT d.devno, d.devname, p.productdate, p.workclass, p.productcount, p.createtime
            FROM devproductcount p
            LEFT JOIN devinfotable d ON p.devno = d.devno
            {where_clause}
            ORDER BY p.createtime DESC
        """

        cursor.execute(query, params)

        production_data = []
        for row in cursor.fetchall():
            production_data.append({
                'devno': row[0],
                'devname': row[1] or '',
                'productdate': row[2].strftime('%Y-%m-%d') if row[2] else '',
                'workclass': row[3] or '',
                'productcount': row[4] or 0,
                'createtime': row[5].strftime('%Y-%m-%d %H:%M:%S') if row[5] else ''
            })

        conn.close()
        return jsonify({'success': True, 'data': production_data})

    except Exception as e:
        return jsonify({'success': False, 'message': f'导出数据失败: {str(e)}'})

# 静态文件路由
@app.route('/admin.css')
def serve_admin_css():
    return send_from_directory('.', 'admin.css')

@app.route('/admin.js')
def serve_admin_js():
    return send_from_directory('.', 'admin.js')

# HTML模板
LOGIN_TEMPLATE = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能制造管理系统 - 登录</title>
    <link rel="stylesheet" href="/admin.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="login-body">
    <div class="login-container">
        <div class="login-box">
            <div class="login-header">
                <div class="system-logo">
                    <div class="logo-icon">🏭</div>
                    <h2>智能制造管理系统</h2>
                    <p class="system-subtitle">Industrial Intelligence Platform</p>
                </div>
            </div>
            <form id="loginForm">
                <div class="form-group">
                    <label for="username">用户名</label>
                    <input type="text" id="username" name="username" placeholder="请输入用户名" required>
                </div>
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" name="password" placeholder="请输入密码" required>
                </div>
                <button type="submit" class="login-btn">
                    <span class="btn-text">登录系统</span>
                </button>
            </form>
            <div id="message" class="message"></div>
            <div class="login-footer">
                <p>© 2025 智能制造管理系统 | 安全可靠</p>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const messageDiv = document.getElementById('message');
            const loginBtn = document.querySelector('.login-btn');
            const btnText = document.querySelector('.btn-text');

            // 清除之前的消息
            messageDiv.textContent = '';
            messageDiv.className = 'message';

            // 添加加载状态
            loginBtn.classList.add('loading');
            btnText.textContent = '登录中...';

            try {
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });

                const result = await response.json();

                if (result.success) {
                    messageDiv.textContent = result.message;
                    messageDiv.className = 'message success';
                    btnText.textContent = '登录成功';
                    setTimeout(() => {
                        window.location.href = '/dashboard';
                    }, 1500);
                } else {
                    messageDiv.textContent = result.message;
                    messageDiv.className = 'message error';
                    // 恢复按钮状态
                    loginBtn.classList.remove('loading');
                    btnText.textContent = '登录系统';
                }
            } catch (error) {
                messageDiv.textContent = '网络连接失败，请检查网络后重试';
                messageDiv.className = 'message error';
                // 恢复按钮状态
                loginBtn.classList.remove('loading');
                btnText.textContent = '登录系统';
            }
        });

        // 添加输入框焦点效果
        document.querySelectorAll('.form-group input').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });

            input.addEventListener('blur', function() {
                if (!this.value) {
                    this.parentElement.classList.remove('focused');
                }
            });
        });

        // 页面加载动画
        window.addEventListener('load', function() {
            document.querySelector('.login-box').style.animation = 'slideInUp 0.6s ease-out';
        });
    </script>

    <style>
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</body>
</html>
'''

DASHBOARD_TEMPLATE = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>后台管理系统</title>
    <link rel="stylesheet" href="/admin.css">
</head>
<body>
    <div class="admin-container">
        <!-- 左侧导航栏 -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <h3>后台管理</h3>
            </div>
            <ul class="sidebar-menu">
                <li><a href="#" onclick="showSection('dashboard')" class="active">仪表盘</a></li>
                
                <li class="has-submenu">
                    <a href="#" onclick="toggleSubmenu('export')">数据导出 <span class="submenu-arrow">▶</span></a>
                    <ul class="submenu" id="export-submenu">
                        <li><a href="#" onclick="showSection('production-export')">加工信息导出</a></li>
                        <li><a href="#" onclick="showSection('alarm-export')">报警信息导出</a></li>
                    </ul>
                </li>
                
                <li class="has-submenu">
                    <a href="#" onclick="toggleSubmenu('tool')">刀具信息 <span class="submenu-arrow">▶</span></a>
                    <ul class="submenu" id="tool-submenu">
                        <li><a href="#" onclick="showSection('tool-use')">加载刀具信息</a></li>
                    </ul>
                </li>
                
                <li><a href="#" onclick="showSection('profile')">个人设置</a></li>
                <li class="admin-only"><a href="#" onclick="showSection('users')">用户管理</a></li>
            </ul>
        </nav>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 顶部导航 -->
            <header class="top-header">
                <div class="header-left">
                    <h1 id="page-title">仪表盘</h1>
                </div>
                <div class="header-right">
                    <span class="user-info">欢迎，<span id="current-user"></span></span>
                    <a href="/logout" class="logout-btn">退出</a>
                </div>
            </header>

            <!-- 内容区域 -->
            <main class="content">
                <!-- 仪表盘 -->
                <section id="dashboard-section" class="content-section active">
                    <div class="dashboard-cards">
                        <div class="card">
                            <h3>系统概览</h3>
                            <p>欢迎使用后台管理系统</p>
                        </div>
                    </div>
                </section>

                <!-- 用户管理 -->
                <section id="users-section" class="content-section admin-only">
                    <div class="section-header">
                        <h2>用户管理</h2>
                        <button onclick="showAddUserModal()" class="btn btn-primary">添加用户</button>
                    </div>
                    <div class="table-container">
                        <table id="users-table">
                            <thead>
                                <tr>
                                    <th>用户名</th>
                                    <th>权限</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </section>

                <!-- 加工信息导出 -->
                <section id="production-export-section" class="content-section">
                    <div class="section-header">
                        <h2>加工信息导出</h2>
                    </div>
                    <div class="export-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="prod-start-date">开始日期</label>
                                <input type="date" id="prod-start-date">
                            </div>
                            <div class="form-group">
                                <label for="prod-end-date">结束日期</label>
                                <input type="date" id="prod-end-date">
                            </div>
                            <div class="form-group">
                                <label for="prod-line-select">生产线</label>
                                <select id="prod-line-select">
                                    <option value="">全部生产线</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="prod-device-select">设备</label>
                                <select id="prod-device-select">
                                    <option value="">全部设备</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <button onclick="exportProductionData()" class="btn btn-primary">导出数据</button>
                                <button onclick="clearProductionFilters()" class="btn btn-secondary">清空筛选</button>
                            </div>
                        </div>
                    </div>
                    <div id="production-export-result" class="export-result"></div>
                </section>

                <!-- 报警信息导出 -->
                <section id="alarm-export-section" class="content-section">
                    <div class="section-header">
                        <h2>报警信息导出</h2>
                    </div>
                    <div class="export-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="alarm-start-date">开始日期</label>
                                <input type="date" id="alarm-start-date">
                            </div>
                            <div class="form-group">
                                <label for="alarm-end-date">结束日期</label>
                                <input type="date" id="alarm-end-date">
                            </div>
                            <div class="form-group">
                                <label for="alarm-line-select">生产线</label>
                                <select id="alarm-line-select">
                                    <option value="">全部生产线</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="alarm-device-select">设备</label>
                                <select id="alarm-device-select">
                                    <option value="">全部设备</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <button onclick="exportAlarmData()" class="btn btn-primary">导出数据</button>
                                <button onclick="clearAlarmFilters()" class="btn btn-secondary">清空筛选</button>
                            </div>
                        </div>
                    </div>
                    <div id="alarm-export-result" class="export-result"></div>
                </section>

                <!-- 刀具信息 -->
                <section id="tool-use-section" class="content-section">
                    <div class="section-header">
                        <h2>刀具使用信息</h2>
                    </div>
                    <div class="tool-filter-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="tool-line-select">生产线</label>
                                <select id="tool-line-select">
                                    <option value="">全部生产线</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="tool-device-select">设备</label>
                                <select id="tool-device-select">
                                    <option value="">全部设备</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="tool-start-date">开始日期</label>
                                <input type="date" id="tool-start-date">
                            </div>
                            <div class="form-group">
                                <label for="tool-end-date">结束日期</label>
                                <input type="date" id="tool-end-date">
                            </div>
                            <div class="form-group">
                                <button onclick="loadToolUseData()" class="btn btn-primary">加载数据</button>
                                <button onclick="exportToolUseData()" class="btn btn-success">导出Excel</button>
                                <button onclick="clearToolFilters()" class="btn btn-secondary">清空筛选</button>
                            </div>
                        </div>
                    </div>
                    <div class="tool-summary" id="tool-summary" style="display: none;">
                        <div class="summary-cards">
                            <div class="summary-card">
                                <h4>总工具数量</h4>
                                <span class="summary-value" id="total-tools">-</span>
                            </div>
                            <div class="summary-card">
                                <h4>总使用次数</h4>
                                <span class="summary-value" id="total-use-count">-</span>
                            </div>
                            <div class="summary-card">
                                <h4>平均使用次数</h4>
                                <span class="summary-value" id="average-use-count">-</span>
                            </div>
                        </div>
                    </div>
                    <div id="tool-use-result" class="export-result"></div>
                </section>

                <!-- 数据导出 (保持向后兼容) -->
                <section id="export-section" class="content-section">
                    <div class="section-header">
                        <h2>加工数据导出</h2>
                    </div>
                    <div class="export-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="start-date">开始日期</label>
                                <input type="date" id="start-date">
                            </div>
                            <div class="form-group">
                                <label for="end-date">结束日期</label>
                                <input type="date" id="end-date">
                            </div>
                            <div class="form-group">
                                <button onclick="exportData()" class="btn btn-primary">导出数据</button>
                            </div>
                        </div>
                    </div>
                    <div id="export-result" class="export-result"></div>
                </section>

                <!-- 个人设置 -->
                <section id="profile-section" class="content-section">
                    <div class="section-header">
                        <h2>个人设置</h2>
                    </div>
                    <div class="profile-form">
                        <h3>修改密码</h3>
                        <form id="change-password-form">
                            <div class="form-group">
                                <label for="old-password">当前密码</label>
                                <input type="password" id="old-password" required>
                            </div>
                            <div class="form-group">
                                <label for="new-password">新密码</label>
                                <input type="password" id="new-password" required>
                            </div>
                            <div class="form-group">
                                <label for="confirm-password">确认新密码</label>
                                <input type="password" id="confirm-password" required>
                            </div>
                            <button type="submit" class="btn btn-primary">修改密码</button>
                        </form>
                    </div>
                </section>
            </main>
        </div>
    </div>

    <!-- 添加用户模态框 -->
    <div id="add-user-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>添加用户</h3>
                <span class="close" onclick="closeAddUserModal()">&times;</span>
            </div>
            <form id="add-user-form">
                <div class="form-group">
                    <label for="new-username">用户名</label>
                    <input type="text" id="new-username" required>
                </div>
                <div class="form-group">
                    <label for="new-user-password">密码</label>
                    <input type="password" id="new-user-password" required>
                </div>
                <div class="form-group">
                    <label for="new-user-admin">权限</label>
                    <select id="new-user-admin">
                        <option value="N">普通用户</option>
                        <option value="Y">管理员</option>
                    </select>
                </div>
                <div class="form-actions">
                    <button type="button" onclick="closeAddUserModal()" class="btn btn-secondary">取消</button>
                    <button type="submit" class="btn btn-primary">添加</button>
                </div>
            </form>
        </div>
    </div>

    <script src="/admin.js"></script>
</body>
</html>
'''

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5001)
