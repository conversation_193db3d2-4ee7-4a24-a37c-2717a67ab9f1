<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能制造管理系统 - 登录预览</title>
    <link rel="stylesheet" href="admin.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="login-body">
    <div class="login-container">
        <div class="login-box">
            <div class="login-header">
                <div class="system-logo">
                    <h2>智能制造管理系统</h2>
                    <p class="system-subtitle">INDUSTRIAL INTELLIGENCE PLATFORM</p>
                </div>
            </div>
            <form id="loginForm">
                <div class="form-group">
                    <input type="text" id="username" name="username" placeholder="请输入用户名" required>
                </div>
                <div class="form-group">
                    <input type="password" id="password" name="password" placeholder="请输入密码" required>
                </div>
                <button type="submit" class="login-btn">
                    <span class="btn-text">登录系统</span>
                </button>
            </form>
            <div id="message" class="message"></div>
            <div class="login-footer">
                <p>© 2025 智能制造管理系统 | 安全可靠</p>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const messageDiv = document.getElementById('message');
            const loginBtn = document.querySelector('.login-btn');
            const btnText = document.querySelector('.btn-text');

            // 清除之前的消息
            messageDiv.textContent = '';
            messageDiv.className = 'message';

            // 添加加载状态
            loginBtn.classList.add('loading');
            btnText.textContent = '登录中...';

            // 模拟登录过程
            setTimeout(() => {
                if (username === 'admin' && password === 'admin') {
                    messageDiv.textContent = '登录成功！正在跳转...';
                    messageDiv.className = 'message success';
                    btnText.textContent = '登录成功';
                    setTimeout(() => {
                        alert('这是预览页面，实际登录请访问 /admin/login');
                        // 恢复按钮状态
                        loginBtn.classList.remove('loading');
                        btnText.textContent = '登录系统';
                    }, 1500);
                } else {
                    messageDiv.textContent = '用户名或密码错误（预览：admin/admin）';
                    messageDiv.className = 'message error';
                    // 恢复按钮状态
                    loginBtn.classList.remove('loading');
                    btnText.textContent = '登录系统';
                }
            }, 1500);
        });

        // 添加输入框焦点效果
        document.querySelectorAll('.form-group input').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });
            
            input.addEventListener('blur', function() {
                if (!this.value) {
                    this.parentElement.classList.remove('focused');
                }
            });
        });

        // 页面加载动画
        window.addEventListener('load', function() {
            document.querySelector('.login-box').style.animation = 'slideInUp 0.6s ease-out';
        });
    </script>

    <style>
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</body>
</html>
